# ES Query Processor

一个用于处理Elasticsearch查询的Python工具，从CSV文件读取signal_id，查询ES并提取data字段。

## 功能特性

- ✅ **安全性**: 使用环境变量管理敏感信息
- ✅ **健壮性**: 完善的错误处理和重试机制
- ✅ **可配置性**: 支持环境变量配置所有参数
- ✅ **日志记录**: 结构化日志，支持文件和控制台输出
- ✅ **类型安全**: 完整的类型注解
- ✅ **连接管理**: HTTP连接池和超时控制
- ✅ **数据验证**: 输入数据验证和错误处理

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

### 1. 环境变量配置

复制示例配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置你的配置：

```bash
# Elasticsearch配置
ES_URL=http://your-es-host:9200
ES_USERNAME=your-username
ES_PASSWORD=your-password
ES_TIMEOUT=30
ES_MAX_RETRIES=3
ES_RETRY_BACKOFF=0.3

# 查询配置
QUERY_START_DATE=2024-12-04T16:00:00.000Z
QUERY_END_DATE=2025-03-05T11:55:24.419Z
QUERY_SIZE=500
TRACK_TOTAL_HITS=false

# 文件配置
INPUT_CSV=input_signals.csv
OUTPUT_FILE=extracted_data.json
FILE_ENCODING=utf-8
```

### 2. 输入文件格式

CSV文件应包含以下列：
- `signal_id`: 必需，信号ID
- `entid`: 可选，实体ID

示例：
```csv
signal_id,entid
signal_001,entity_001
signal_002,entity_002
```

## 使用方法

### 1. 设置环境变量

```bash
# 方法1: 使用.env文件（推荐）
source .env

# 方法2: 直接设置
export ES_URL="http://your-es-host:9200"
export ES_USERNAME="your-username"
export ES_PASSWORD="your-password"
```

### 2. 运行程序

```bash
python es_query_processor.py
```

## 输出

- **日志文件**: `es_query_processor.log`
- **数据文件**: 配置的输出文件（默认：`extracted_data.json`）
- **控制台**: 实时处理状态

## 错误处理

程序包含完善的错误处理：

- **网络错误**: 自动重试机制
- **认证错误**: 详细的错误信息
- **数据验证**: 输入数据格式验证
- **文件错误**: 文件权限和存在性检查

## 性能优化

- **连接池**: 复用HTTP连接
- **重试策略**: 指数退避重试
- **超时控制**: 防止长时间等待
- **内存优化**: 流式处理大文件

## 故障排除

### 常见问题

1. **连接超时**
   - 检查ES_URL是否正确
   - 增加ES_TIMEOUT值

2. **认证失败**
   - 验证ES_USERNAME和ES_PASSWORD
   - 检查ES集群是否需要认证

3. **文件权限错误**
   - 检查输入文件是否存在
   - 确保有输出目录的写权限

### 日志级别

修改日志级别以获取更多信息：

```python
logging.basicConfig(level=logging.DEBUG)  # 详细调试信息
logging.basicConfig(level=logging.INFO)   # 一般信息（默认）
logging.basicConfig(level=logging.WARNING) # 仅警告和错误
```

## 开发

### 代码结构

- `ESConfig`: Elasticsearch配置管理
- `QueryConfig`: 查询参数配置
- `FileConfig`: 文件路径配置
- `ESQueryProcessor`: 主要处理逻辑

### 扩展功能

可以轻松扩展以下功能：
- 批量查询优化
- 多线程处理
- 不同的输出格式
- 自定义查询模板
