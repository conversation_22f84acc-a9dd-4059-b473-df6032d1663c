#!/usr/bin/env python3
"""
ES Query Processor 测试文件
"""

import os
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from es_query_processor import ESConfig, QueryConfig, FileConfig, ESQueryProcessor


class TestESQueryProcessor(unittest.TestCase):
    """ES查询处理器测试类"""

    def setUp(self):
        """测试前准备"""
        self.es_config = ESConfig(
            url="http://test-es:9200",
            username="test_user",
            password="test_pass",
            timeout=10
        )
        self.query_config = QueryConfig(
            start_date="2024-01-01T00:00:00.000Z",
            end_date="2024-12-31T23:59:59.999Z",
            size=100
        )
        self.file_config = FileConfig(
            input_csv="test_input.csv",
            output_file="test_output.json"
        )
        
    def test_config_creation(self):
        """测试配置类创建"""
        self.assertEqual(self.es_config.url, "http://test-es:9200")
        self.assertEqual(self.es_config.username, "test_user")
        self.assertEqual(self.query_config.size, 100)
        
    def test_build_es_query(self):
        """测试ES查询构建"""
        processor = ESQueryProcessor(self.es_config, self.query_config, self.file_config)
        
        query = processor.build_es_query("test_signal_001")
        
        # 验证查询结构
        self.assertIn("query", query)
        self.assertIn("bool", query["query"])
        self.assertIn("filter", query["query"]["bool"])
        
        # 验证signal_id过滤
        filters = query["query"]["bool"]["filter"]
        signal_filter = next((f for f in filters if "match_phrase" in f), None)
        self.assertIsNotNone(signal_filter)
        self.assertEqual(signal_filter["match_phrase"]["signal_id"], "test_signal_001")
        
    def test_build_es_query_empty_signal_id(self):
        """测试空signal_id的处理"""
        processor = ESQueryProcessor(self.es_config, self.query_config, self.file_config)
        
        with self.assertRaises(ValueError):
            processor.build_es_query("")
            
        with self.assertRaises(ValueError):
            processor.build_es_query("   ")
            
    def test_validate_csv_row(self):
        """测试CSV行验证"""
        processor = ESQueryProcessor(self.es_config, self.query_config, self.file_config)
        
        # 正常情况
        valid_row = {"signal_id": "test_001", "entid": "entity_001"}
        result = processor._validate_csv_row(valid_row, 1)
        self.assertEqual(result, "test_001")
        
        # 缺少signal_id
        invalid_row = {"entid": "entity_001"}
        result = processor._validate_csv_row(invalid_row, 1)
        self.assertIsNone(result)
        
        # signal_id为空
        empty_row = {"signal_id": "", "entid": "entity_001"}
        result = processor._validate_csv_row(empty_row, 1)
        self.assertIsNone(result)
        
    def test_extract_data_field(self):
        """测试数据字段提取"""
        processor = ESQueryProcessor(self.es_config, self.query_config, self.file_config)
        
        # 正常情况
        mock_result = {
            "hits": {
                "hits": [
                    {
                        "fields": {
                            "data": ["test_data_content"]
                        }
                    }
                ]
            }
        }
        result = processor._extract_data_field(mock_result)
        self.assertEqual(result, "test_data_content")
        
        # 空结果
        empty_result = {"hits": {"hits": []}}
        result = processor._extract_data_field(empty_result)
        self.assertIsNone(result)
        
        # 缺少data字段
        no_data_result = {
            "hits": {
                "hits": [
                    {
                        "fields": {
                            "other_field": ["other_value"]
                        }
                    }
                ]
            }
        }
        result = processor._extract_data_field(no_data_result)
        self.assertIsNone(result)

    @patch('es_query_processor.requests.Session')
    def test_execute_query_success(self, mock_session_class):
        """测试查询执行成功"""
        # 模拟成功响应
        mock_session = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"hits": {"hits": []}}
        mock_session.post.return_value = mock_response
        mock_session_class.return_value = mock_session
        
        processor = ESQueryProcessor(self.es_config, self.query_config, self.file_config)
        result = processor._execute_query("test_signal")
        
        self.assertIsNotNone(result)
        self.assertEqual(result, {"hits": {"hits": []}})

    def test_create_test_csv(self):
        """创建测试CSV文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write("signal_id,entid\n")
            f.write("test_signal_001,entity_001\n")
            f.write("test_signal_002,entity_002\n")
            return f.name


def create_sample_input_file():
    """创建示例输入文件"""
    sample_content = """signal_id,entid
signal_001,entity_001
signal_002,entity_002
signal_003,entity_003"""
    
    with open("input_signals.csv", "w", encoding="utf-8") as f:
        f.write(sample_content)
    
    print("已创建示例输入文件: input_signals.csv")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "create-sample":
        create_sample_input_file()
    else:
        unittest.main()
